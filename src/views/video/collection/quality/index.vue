<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <template #toolbar:after>
      <!-- <el-button type="primary" size="small" @click="handleCreateTask">创建新任务</el-button> -->
      <el-button type="default" size="small" @click="handleRecheck">重新检测</el-button>
    </template>

    <!-- 文件状态列自定义渲染 -->
    <template #table:value6:simple="{ row }">
      <el-tag :type="getStatusType(row.value6)">
        {{ row.value6 }}
      </el-tag>
    </template>

    <!-- 文件格式列自定义渲染 -->
    <template #table:value5:simple="{ row }">
      <el-tag type="primary">
        {{ row.value5 }}
      </el-tag>
    </template>

    <!-- 时长检测状态列自定义渲染 -->
    <template #table:value9:simple="{ row }">
      <el-tag :type="getDetectionStatusType(row.value9)">
        {{ row.value9 }}
      </el-tag>
    </template>

    <!-- 解码检测状态列自定义渲染 -->
    <template #table:value11:simple="{ row }">
      <el-tag :type="getDetectionStatusType(row.value11)">
        {{ row.value11 }}
      </el-tag>
    </template>

    <!-- 失败原因列自定义渲染 -->
    <template #table:value12:simple="{ row }">
      <span v-if="row.value12" class="text-red-500">{{ row.value12 }}</span>
      <span v-else class="text-gray-400">-</span>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { fileFormat, fileStatus } from '@/dicts/video/index.js'

export default {
  name: 'VideoQualityDetection',
  data() {
    return {
      tableType: 'video_quality_detection'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '质量检测任务',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },
        model: {
          id: {
            type: 'text',
            label: '视频文件编号',
            width: 120,
            fixed: 'left',
            search: { hidden: true },
            form: { hidden: true }
          },
          value1: {
            type: 'text',
            label: '任务编号',
            width: 150,
            search: { hidden: true },
            form: { hidden: true }
          },
          value2: {
            type: 'text',
            label: '视频文件名',
            width: 200,
            showOverflowTooltip: true,
            search: {
              type: 'input',
              placeholder: '请输入文件名'
            },
            form: { hidden: true }
          },
          value3: {
            type: 'text',
            label: '文件大小',
            width: 120,
            search: { hidden: true },
            form: { hidden: true }
          },
          value5: {
            type: 'text',
            label: '文件格式',
            width: 100,
            search: {
              type: 'select',
              placeholder: '全部格式',
              clearable: true,
              options: [
                { label: '全部格式', value: '' },
                ...fileFormat
              ]
            },
            form: { hidden: true }
          },
          value6: {
            type: 'text',
            label: '文件状态',
            width: 100,
            search: {
              type: 'select',
              placeholder: '全部状态',
              clearable: true,
              options: [
                { label: '全部状态', value: '' },
                ...fileStatus
              ]
            },
            form: { hidden: true }
          },
          value7: {
            type: 'text',
            label: '检测时间',
            width: 160,
            search: { hidden: true },
            form: { hidden: true }
          },
          value8: {
            type: 'text',
            label: '视频时长',
            width: 100,
            search: { hidden: true },
            form: { hidden: true }
          },
          value9: {
            type: 'text',
            label: '时长检测状态',
            width: 120,
            search: { hidden: true },
            form: { hidden: true }
          },
          value10: {
            type: 'text',
            label: '帧率',
            width: 100,
            search: { hidden: true },
            form: { hidden: true }
          },
          value11: {
            type: 'text',
            label: '解码检测状态',
            width: 120,
            search: { hidden: true },
            form: { hidden: true }
          },
          value12: {
            type: 'text',
            label: '失败原因',
            width: 200,
            showOverflowTooltip: true,
            search: { hidden: true },
            form: { hidden: true }
          }
        }
      }
    }
  },
  methods: {
    getStatusType(status) {
      const statusMap = {
        '有效': 'success',
        '已删除': 'info',
        '损坏': 'danger',
        '待归档': 'warning'
      }
      return statusMap[status] || 'info'
    },
    getDetectionStatusType(status) {
      const statusMap = {
        '成功': 'success',
        '检测成功': 'success',
        '失败': 'danger',
        '检测失败': 'danger',
        '进行中': 'warning',
        '检测中': 'warning',
        '待检测': 'info'
      }
      return statusMap[status] || 'info'
    },
    handleCreateTask() {
      this.$message.info('创建新任务功能开发中...')
    },
    handleRecheck() {
      this.$message.info('重新检测功能开发中...')
    },
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
